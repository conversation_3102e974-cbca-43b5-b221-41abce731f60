#!/bin/bash

# KnowledgeAI Full Stack Development Startup

echo "🚀 Starting KnowledgeAI Full Stack Development Environment"
echo ""

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -d "backend" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

# Create .env.local if it doesn't exist
if [ ! -f ".env.local" ]; then
    echo "📝 Creating .env.local from example..."
    cp .env.local.example .env.local
fi

# Check backend .env
if [ ! -f "backend/.env" ]; then
    echo "📝 Creating backend .env from example..."
    cp backend/.env.example backend/.env
    echo ""
    echo "⚠️  IMPORTANT: Please edit backend/.env and add your Gemini API key:"
    echo "   LANGEXTRACT_API_KEY=your-api-key-here"
    echo ""
    echo "   Get your API key from: https://aistudio.google.com/app/apikey"
    echo ""
    read -p "Press Enter after you've added your API key to continue..."
fi

echo "📦 Installing frontend dependencies..."
pnpm install

echo ""
echo "🔥 Starting development servers..."
echo ""
echo "   Frontend: http://localhost:3000"
echo "   Backend:  http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo ""
echo "Press Ctrl+C to stop all servers"
echo ""

# Start both frontend and backend concurrently
pnpm run dev:full