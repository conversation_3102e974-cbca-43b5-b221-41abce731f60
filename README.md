# KnowledgeAI - Fast Document Processing Application

A high-performance Document Processing Application (DPA) built on top of Google's LangExtract library, integrated with a modern Next.js frontend for intelligent knowledge extraction from unstructured documents.

## 🚀 Features

- **AI-Powered Extraction**: Uses Google's LangExtract with Gemini models for precise knowledge extraction
- **Multiple Extraction Types**: Pre-configured templates for general, medical, legal, and financial documents
- **Real-time Processing**: Fast async processing with job queuing and progress tracking
- **Interactive UI**: Modern React interface with drag-and-drop file upload
- **Source Grounding**: Every extraction is mapped back to its exact location in the source text
- **Structured Output**: Consistent JSON output with metadata and confidence scores
- **File Support**: Text files (.txt, .md) and PDF files with IBM Docling integration
- **Visualization**: Interactive results viewer with grouped extractions

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Next.js       │    │   FastAPI       │    │   LangExtract   │
│   Frontend      │◄──►│   Backend       │◄──►│   + Gemini      │
│                 │    │                 │    │                 │
│ • React UI      │    │ • REST API      │    │ • AI Extraction │
│ • File Upload   │    │ • Job Queue     │    │ • Source Ground │
│ • Results View  │    │ • Progress      │    │ • Structured    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Quick Start

### Prerequisites

- **Node.js 18+** (for frontend)
- **Python 3.11+** (for backend)
- **Gemini API Key** (get from [AI Studio](https://aistudio.google.com/app/apikey))

### 1. Clone and Setup

```bash
git clone <your-repo>
cd knowledgeai
```

### 2. Backend Setup

```bash
cd backend

# Copy environment template
cp .env.example .env

# Edit .env and add your Gemini API key
# LANGEXTRACT_API_KEY=your-api-key-here

# Start the backend (this will create venv and install dependencies)
./start.sh
```

The backend will be available at `http://localhost:8000`

### 3. Frontend Setup

```bash
# In a new terminal
cd frontend  # or root directory if Next.js is in root

# Install dependencies
pnpm install

# Start development server
pnpm dev
```

The frontend will be available at `http://localhost:3000`

## 📖 Usage

### Web Interface

1. **Open** `http://localhost:3000` in your browser
2. **Select** extraction type (general, medical, legal, financial)
3. **Upload** a document or paste text directly
4. **Click** "Extract Knowledge" to start processing
5. **View** results in the interactive dialog
6. **Download** results as JSON

### API Endpoints

#### Health Check
```bash
curl http://localhost:8000/
```

#### Get Extraction Types
```bash
curl http://localhost:8000/api/extraction-types
```

#### Extract from Text
```bash
curl -X POST http://localhost:8000/api/extract \
  -H "Content-Type: application/json" \
  -d '{
    "text": "John Smith, CEO of TechCorp, announced a $50M funding round.",
    "extraction_type": "general",
    "model_id": "gemini-2.5-flash"
  }'
```

#### Extract from File
```bash
curl -X POST http://localhost:8000/api/extract-file \
  -F "file=@document.txt" \
  -F "extraction_type=general"
```

#### Analyze PDF Structure
```bash
curl -X POST http://localhost:8000/api/analyze-pdf \
  -F "file=@document.pdf"
```

#### Check Job Status
```bash
curl http://localhost:8000/api/job/{job_id}
```

## 📄 PDF Processing with IBM Docling

Our PDF support uses IBM's state-of-the-art Docling library for advanced document processing:

### Key Features
- **Structure Preservation**: Maintains document layout and hierarchy
- **Table Extraction**: Automatically detects and extracts table data  
- **OCR Support**: Handles scanned PDFs with built-in OCR
- **Metadata Extraction**: Pulls document properties and creation info
- **Smart Recommendations**: Suggests optimal extraction type based on content

### PDF Analysis
The system provides detailed PDF analysis before extraction:
- Document metadata (title, author, pages, dates)
- Structure analysis (tables, images, sections)
- Content preview and recommendations
- Extraction readiness assessment

### Supported PDF Features
- ✅ Text extraction with layout preservation
- ✅ Table detection and structure analysis
- ✅ Metadata extraction (title, author, dates)
- ✅ Section/heading detection
- ✅ Content-based extraction type recommendations
- ✅ OCR for scanned documents

## 🔧 Configuration

### Environment Variables

**Backend (.env)**
```bash
# Required
LANGEXTRACT_API_KEY=your-gemini-api-key

# Optional
REDIS_URL=redis://localhost:6379
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
ALLOWED_ORIGINS=http://localhost:3000
```

**Frontend (.env.local)**
```bash
NEXT_PUBLIC_API_URL=http://localhost:8000
```

### Extraction Types

The system comes with 4 pre-configured extraction templates:

1. **General**: Entities, concepts, relationships, facts
2. **Medical**: Medications, dosages, conditions, procedures
3. **Legal**: Clauses, obligations, dates, terms
4. **Financial**: Amounts, percentages, metrics, instruments

## 🚀 Deployment

### Docker Deployment

```bash
cd backend

# Build and run with Docker Compose
docker-compose up --build

# Or run individual services
docker build -t knowledgeai-backend .
docker run -p 8000:8000 -e LANGEXTRACT_API_KEY=your-key knowledgeai-backend
```

### Production Deployment

1. **Backend**: Deploy FastAPI to services like Railway, Render, or AWS
2. **Frontend**: Deploy Next.js to Vercel, Netlify, or similar
3. **Database**: Use Redis Cloud or AWS ElastiCache for job queue
4. **Scaling**: Add Celery workers for high-volume processing

## 📊 Performance

- **Processing Speed**: ~2-5 seconds for typical documents (1-10KB)
- **Throughput**: Supports parallel processing with configurable workers
- **Accuracy**: Leverages Gemini's state-of-the-art language understanding
- **Scalability**: Async job queue handles concurrent requests

## 🔍 Example Extractions

### Input Text
```
"Dr. Sarah Johnson prescribed Metformin 500mg twice daily to treat the patient's Type 2 diabetes. The next appointment is scheduled for March 15, 2024."
```

### Output (Medical Extraction)
```json
{
  "extractions": [
    {
      "class": "person",
      "text": "Dr. Sarah Johnson",
      "attributes": {"role": "doctor"},
      "start_char": 0,
      "end_char": 16
    },
    {
      "class": "medication",
      "text": "Metformin",
      "attributes": {"dosage": "500mg", "frequency": "twice daily"},
      "start_char": 27,
      "end_char": 36
    },
    {
      "class": "condition",
      "text": "Type 2 diabetes",
      "attributes": {"type": "chronic condition"},
      "start_char": 85,
      "end_char": 100
    }
  ],
  "metadata": {
    "total_extractions": 3,
    "text_length": 150,
    "model_used": "gemini-2.5-flash",
    "extraction_type": "medical"
  }
}
```

## 🛡️ Security

- **API Key Protection**: Environment variables for sensitive data
- **CORS Configuration**: Restricted origins for production
- **Input Validation**: Pydantic models for request validation
- **File Type Restrictions**: Limited to safe file formats
- **Rate Limiting**: Built-in FastAPI rate limiting (configurable)

## 🔮 Roadmap

- [ ] **PDF Support**: Add PDF parsing with PyPDF2/pdfplumber
- [ ] **Batch Processing**: Multiple file upload and processing
- [ ] **Custom Templates**: User-defined extraction templates
- [ ] **Authentication**: User accounts and API keys
- [ ] **Analytics**: Usage metrics and extraction insights
- [ ] **Webhooks**: Real-time notifications for job completion
- [ ] **Export Formats**: CSV, Excel, and other output formats

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Google LangExtract**: The powerful extraction engine
- **Gemini API**: State-of-the-art language models
- **shadcn/ui**: Beautiful React components
- **FastAPI**: High-performance Python web framework
- **Next.js**: The React framework for production

## 📞 Support

- **Documentation**: Check the `/docs` endpoint at `http://localhost:8000/docs`
- **Issues**: Open an issue on GitHub
- **API Reference**: Interactive docs at `http://localhost:8000/docs`

---

**Built with ❤️ using Google's LangExtract and modern web technologies**