/**
 * API client for KnowledgeAI DPA backend
 */

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export interface ExtractionRequest {
  text: string;
  extraction_type?: string;
  custom_prompt?: string;
  model_id?: string;
}

export interface ExtractionJob {
  job_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  created_at: string;
  completed_at?: string;
  result?: ExtractionResult;
  error?: string;
}

export interface ExtractionResult {
  extractions: Array<{
    class: string;
    text: string;
    attributes: Record<string, any>;
    start_char: number;
    end_char: number;
    confidence?: number;
  }>;
  metadata: {
    total_extractions: number;
    text_length: number;
    model_used: string;
    extraction_type: string;
  };
}

export interface ExtractionType {
  types: string[];
  templates: Record<string, { prompt: string }>;
}

export interface PdfAnalysis {
  filename: string;
  size_bytes: number;
  text_length: number;
  metadata: {
    title: string;
    author: string;
    page_count: number;
    creation_date: string;
    modification_date: string;
    subject: string;
    keywords: string;
  };
  structure: {
    has_tables: boolean;
    has_images: boolean;
    sections: Array<{
      text: string;
      level: number;
      page: number;
    }>;
    tables: Array<{
      table_id: number;
      page: number;
      rows: number;
      cols: number;
      text: string;
    }>;
  };
  preview: string;
  extraction_ready: boolean;
  recommended_extraction_type: string;
}

class ApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Unknown error' }));
      throw new Error(error.detail || `HTTP ${response.status}`);
    }

    return response.json();
  }

  async healthCheck(): Promise<{ message: string; status: string }> {
    return this.request('/');
  }

  async getExtractionTypes(): Promise<ExtractionType> {
    return this.request('/api/extraction-types');
  }

  async extractKnowledge(request: ExtractionRequest): Promise<{ job_id: string; status: string }> {
    return this.request('/api/extract', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async extractFromFile(
    file: File,
    extractionType: string = 'general',
    modelId: string = 'gemini-2.5-flash'
  ): Promise<{ job_id: string; status: string }> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('extraction_type', extractionType);
    formData.append('model_id', modelId);

    const response = await fetch(`${this.baseUrl}/api/extract-file`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Unknown error' }));
      throw new Error(error.detail || `HTTP ${response.status}`);
    }

    return response.json();
  }

  async getJobStatus(jobId: string): Promise<ExtractionJob> {
    return this.request(`/api/job/${jobId}`);
  }

  async listJobs(): Promise<{ jobs: Array<Omit<ExtractionJob, 'result'>> }> {
    return this.request('/api/jobs');
  }

  async deleteJob(jobId: string): Promise<{ message: string }> {
    return this.request(`/api/job/${jobId}`, {
      method: 'DELETE',
    });
  }

  async analyzePdf(file: File): Promise<PdfAnalysis> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch(`${this.baseUrl}/api/analyze-pdf`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Unknown error' }));
      throw new Error(error.detail || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // Polling utility for job completion
  async pollJobCompletion(
    jobId: string,
    onProgress?: (job: ExtractionJob) => void,
    maxAttempts: number = 60,
    intervalMs: number = 2000
  ): Promise<ExtractionJob> {
    let attempts = 0;

    while (attempts < maxAttempts) {
      const job = await this.getJobStatus(jobId);
      
      if (onProgress) {
        onProgress(job);
      }

      if (job.status === 'completed' || job.status === 'failed') {
        return job;
      }

      await new Promise(resolve => setTimeout(resolve, intervalMs));
      attempts++;
    }

    throw new Error('Job polling timeout');
  }
}

export const apiClient = new ApiClient();

// React hooks for API integration
export function useApiClient() {
  return apiClient;
}