{"name": "knowledgeai", "version": "1.0.0", "description": "Fast Document Processing Application using Google's LangExtract", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "backend": "cd backend && ./start.sh", "setup": "pnpm install && cd backend && cp .env.example .env", "dev:full": "concurrently \"pnpm dev\" \"cd backend && ./start.sh\"", "start:dev": "./start-dev.sh"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@tailwindcss/postcss": "^4.1.12", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "concurrently": "^9.2.1", "eslint": "^9.17.0", "eslint-config-next": "15.5.2", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}, "engines": {"node": ">=18.0.0"}}