[debug] [2025-09-02T00:30:27.424Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.426Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.427Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.427Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.428Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.428Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.429Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/ [none]
[debug] [2025-09-02T00:30:27.429Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/ x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-09-02T00:30:27.443Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-09-02T00:30:27.443Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-09-02T00:30:27.444Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseio.com [none]
[debug] [2025-09-02T00:30:27.444Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseio.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-09-02T00:30:27.455Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.455Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.455Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.455Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.455Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.455Z] Checked if tokens are valid: true, expires at: 1756776123912
[debug] [2025-09-02T00:30:27.455Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/ [none]
[debug] [2025-09-02T00:30:27.455Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/ x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-09-02T00:30:27.456Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [none]
[debug] [2025-09-02T00:30:27.456Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-09-02T00:30:27.457Z] >>> [apiv2][query] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseio.com [none]
[debug] [2025-09-02T00:30:27.457Z] >>> [apiv2][(partial)header] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseio.com x-goog-quota-user=projects/po2vf2ae7tal9invaj7jkf4a06hsac
[debug] [2025-09-02T00:30:27.630Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseio.com 403
[debug] [2025-09-02T00:30:27.630Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseio.com [omitted]
[debug] [2025-09-02T00:30:27.659Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseio.com 403
[debug] [2025-09-02T00:30:27.659Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebaseio.com [omitted]
[debug] [2025-09-02T00:30:27.750Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-09-02T00:30:27.750Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-09-02T00:30:27.765Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com 200
[debug] [2025-09-02T00:30:27.765Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/firebasecrashlytics.googleapis.com [omitted]
[debug] [2025-09-02T00:30:27.891Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/ 200
[debug] [2025-09-02T00:30:27.891Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/ [omitted]
[debug] [2025-09-02T00:30:27.963Z] <<< [apiv2][status] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/ 200
[debug] [2025-09-02T00:30:27.964Z] <<< [apiv2][body] GET https://serviceusage.googleapis.com/v1/projects/po2vf2ae7tal9invaj7jkf4a06hsac/services/ [omitted]
[debug] [2025-09-02T00:30:27.966Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-09-02T00:30:27.966Z] > authorizing via signed-in user (<EMAIL>)
