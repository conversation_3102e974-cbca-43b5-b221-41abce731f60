"use client";

import React, { useState, useCallback, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  FileText, 
  Zap, 
  Upload, 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Eye,
  Download,
  Trash2
} from "lucide-react";
import { apiClient, ExtractionJob, ExtractionResult, ExtractionType, PdfAnalysis } from "@/lib/api";
import { PdfAnalyzer } from "@/components/pdf-analyzer";

interface ExtractionDemoProps {
  className?: string;
}

export function ExtractionDemo({ className }: ExtractionDemoProps) {
  const [text, setText] = useState("");
  const [extractionType, setExtractionType] = useState("general");
  const [availableTypes, setAvailableTypes] = useState<ExtractionType | null>(null);
  const [currentJob, setCurrentJob] = useState<ExtractionJob | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showResults, setShowResults] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // Load extraction types on mount
  useEffect(() => {
    const loadExtractionTypes = async () => {
      try {
        const types = await apiClient.getExtractionTypes();
        setAvailableTypes(types);
      } catch (err) {
        console.error('Failed to load extraction types:', err);
      }
    };

    loadExtractionTypes();
  }, []);

  const handleExtraction = useCallback(async () => {
    if (!text.trim()) {
      setError("Please enter some text to extract knowledge from.");
      return;
    }

    setError(null);
    setIsProcessing(true);

    try {
      // Start extraction job
      const { job_id } = await apiClient.extractKnowledge({
        text,
        extraction_type: extractionType,
        model_id: "gemini-2.5-flash"
      });

      // Poll for completion
      const completedJob = await apiClient.pollJobCompletion(
        job_id,
        (job) => {
          setCurrentJob(job);
        }
      );

      setCurrentJob(completedJob);
      
      if (completedJob.status === 'completed') {
        setShowResults(true);
      } else if (completedJob.status === 'failed') {
        setError(completedJob.error || 'Extraction failed');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsProcessing(false);
    }
  }, [text, extractionType]);

  const handleFileUpload = useCallback(async (file: File) => {
    if (!file) return;

    setError(null);
    setIsProcessing(true);

    try {
      const { job_id } = await apiClient.extractFromFile(file, extractionType);
      
      const completedJob = await apiClient.pollJobCompletion(
        job_id,
        (job) => {
          setCurrentJob(job);
        }
      );

      setCurrentJob(completedJob);
      
      if (completedJob.status === 'completed') {
        setShowResults(true);
      } else if (completedJob.status === 'failed') {
        setError(completedJob.error || 'Extraction failed');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'File upload failed');
    } finally {
      setIsProcessing(false);
    }
  }, [extractionType]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0]);
    }
  }, [handleFileUpload]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileUpload(e.target.files[0]);
    }
  }, [handleFileUpload]);

  const resetDemo = useCallback(() => {
    setText("");
    setCurrentJob(null);
    setIsProcessing(false);
    setError(null);
    setShowResults(false);
  }, []);

  const handleExtractFromPdfAnalysis = useCallback(async (analysis: PdfAnalysis) => {
    setError(null);
    setIsProcessing(true);
    
    // Set the recommended extraction type
    setExtractionType(analysis.recommended_extraction_type);

    try {
      // Start extraction job with the PDF text
      const { job_id } = await apiClient.extractKnowledge({
        text: analysis.preview, // Use full text from analysis
        extraction_type: analysis.recommended_extraction_type,
        model_id: "gemini-2.5-flash"
      });

      // Poll for completion
      const completedJob = await apiClient.pollJobCompletion(
        job_id,
        (job) => {
          setCurrentJob(job);
        }
      );

      setCurrentJob(completedJob);
      
      if (completedJob.status === 'completed') {
        setShowResults(true);
      } else if (completedJob.status === 'failed') {
        setError(completedJob.error || 'Extraction failed');
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsProcessing(false);
    }
  }, []);

  const downloadResults = useCallback(() => {
    if (!currentJob?.result) return;

    const dataStr = JSON.stringify(currentJob.result, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `extraction-results-${currentJob.job_id}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }, [currentJob]);

  return (
    <div className={className}>
      <Card className="max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-600" />
            AI Knowledge Extraction Demo
          </CardTitle>
          <CardDescription>
            Extract structured knowledge from documents using Google's LangExtract
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Extraction Type Selector */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Extraction Type</label>
            <Select value={extractionType} onValueChange={setExtractionType}>
              <SelectTrigger>
                <SelectValue placeholder="Select extraction type" />
              </SelectTrigger>
              <SelectContent>
                {availableTypes?.types.map((type) => (
                  <SelectItem key={type} value={type}>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="capitalize">
                        {type}
                      </Badge>
                      <span className="capitalize">{type} extraction</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {availableTypes?.templates[extractionType] && (
              <p className="text-xs text-muted-foreground">
                {availableTypes.templates[extractionType].prompt}
              </p>
            )}
          </div>

          {/* File Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
              dragActive 
                ? "border-blue-400 bg-blue-50 dark:bg-blue-950" 
                : "border-slate-300 dark:border-slate-600 hover:border-blue-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={() => document.getElementById('file-input')?.click()}
          >
            <input
              id="file-input"
              type="file"
              className="hidden"
              accept=".txt,.md,.pdf"
              onChange={handleFileInput}
              disabled={isProcessing}
            />
            <FileText className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-slate-600 dark:text-slate-300 mb-2">
              {dragActive ? "Drop your document here" : "Drag & drop your document here, or click to browse"}
            </p>
            <p className="text-sm text-slate-500">
              Supports: .txt, .md, .pdf files
            </p>
          </div>

          {/* Text Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Or paste your text:</label>
            <Textarea 
              placeholder="Paste your text content here for instant knowledge extraction..."
              className="min-h-[120px]"
              value={text}
              onChange={(e) => setText(e.target.value)}
              disabled={isProcessing}
            />
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Progress Display */}
          {currentJob && isProcessing && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Processing...</span>
                    <Badge variant="outline">{currentJob.status}</Badge>
                  </div>
                  <Progress value={currentJob.progress} className="w-full" />
                  <p className="text-xs text-muted-foreground">
                    Job ID: {currentJob.job_id}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button 
              onClick={handleExtraction}
              disabled={isProcessing || (!text.trim())}
              className="flex-1"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Extract Knowledge
                </>
              )}
            </Button>
            
            {currentJob && (
              <>
                <Button
                  variant="outline"
                  onClick={() => setShowResults(true)}
                  disabled={!currentJob.result}
                >
                  <Eye className="mr-2 h-4 w-4" />
                  View Results
                </Button>
                
                <Button
                  variant="outline"
                  onClick={downloadResults}
                  disabled={!currentJob.result}
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
                
                <Button
                  variant="outline"
                  onClick={resetDemo}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Reset
                </Button>
              </>
            )}
          </div>

          {/* Success Message */}
          {currentJob?.status === 'completed' && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Extraction completed! Found {currentJob.result?.metadata.total_extractions} entities.
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* PDF Analyzer */}
      <div className="mt-8">
        <PdfAnalyzer 
          onExtractFromAnalysis={handleExtractFromPdfAnalysis}
          className="max-w-4xl mx-auto"
        />
      </div>

      {/* Results Dialog */}
      <ResultsDialog 
        job={currentJob}
        open={showResults}
        onOpenChange={setShowResults}
      />
    </div>
  );
}

interface ResultsDialogProps {
  job: ExtractionJob | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

function ResultsDialog({ job, open, onOpenChange }: ResultsDialogProps) {
  if (!job?.result) return null;

  const { result } = job;
  const groupedExtractions = result.extractions.reduce((acc, extraction) => {
    if (!acc[extraction.class]) {
      acc[extraction.class] = [];
    }
    acc[extraction.class].push(extraction);
    return acc;
  }, {} as Record<string, typeof result.extractions>);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Extraction Results</DialogTitle>
          <DialogDescription>
            Found {result.metadata.total_extractions} entities using {result.metadata.model_used}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Metadata */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Metadata</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="font-medium">Total Extractions:</span>
                  <p>{result.metadata.total_extractions}</p>
                </div>
                <div>
                  <span className="font-medium">Text Length:</span>
                  <p>{result.metadata.text_length} chars</p>
                </div>
                <div>
                  <span className="font-medium">Model:</span>
                  <p>{result.metadata.model_used}</p>
                </div>
                <div>
                  <span className="font-medium">Type:</span>
                  <p className="capitalize">{result.metadata.extraction_type}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Grouped Extractions */}
          {Object.entries(groupedExtractions).map(([className, extractions]) => (
            <Card key={className}>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Badge variant="outline" className="capitalize">
                    {className}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    ({extractions.length} items)
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {extractions.map((extraction, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <div className="flex items-start justify-between mb-2">
                        <span className="font-medium text-sm">
                          "{extraction.text}"
                        </span>
                        <Badge variant="secondary" className="text-xs">
                          {extraction.start_char}-{extraction.end_char}
                        </Badge>
                      </div>
                      
                      {Object.keys(extraction.attributes).length > 0 && (
                        <div className="space-y-1">
                          {Object.entries(extraction.attributes).map(([key, value]) => (
                            <div key={key} className="text-xs text-muted-foreground">
                              <span className="font-medium">{key}:</span> {String(value)}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}