"use client";

import React, { useState, useCallback } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  FileText, 
  Upload, 
  Eye,
  FileCheck,
  AlertCircle,
  Loader2,
  Table,
  Image as ImageIcon,
  BookOpen,
  Calendar,
  User,
  Hash,
  Zap
} from "lucide-react";
import { apiClient, PdfAnalysis } from "@/lib/api";

interface PdfAnalyzerProps {
  onExtractFromAnalysis?: (analysis: PdfAnalysis) => void;
  className?: string;
}

export function PdfAnalyzer({ onExtractFromAnalysis, className }: PdfAnalyzerProps) {
  const [analysis, setAnalysis] = useState<PdfAnalysis | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  const handleFileAnalysis = useCallback(async (file: File) => {
    if (!file || file.type !== 'application/pdf') {
      setError("Please select a PDF file");
      return;
    }

    setError(null);
    setIsAnalyzing(true);

    try {
      const result = await apiClient.analyzePdf(file);
      setAnalysis(result);
      setShowDetails(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Analysis failed');
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileAnalysis(e.dataTransfer.files[0]);
    }
  }, [handleFileAnalysis]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFileAnalysis(e.target.files[0]);
    }
  }, [handleFileAnalysis]);

  const handleExtractFromPdf = useCallback(() => {
    if (analysis && onExtractFromAnalysis) {
      onExtractFromAnalysis(analysis);
      setShowDetails(false);
    }
  }, [analysis, onExtractFromAnalysis]);

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileCheck className="h-5 w-5 text-blue-600" />
            PDF Document Analyzer
          </CardTitle>
          <CardDescription>
            Analyze PDF structure and get extraction recommendations using IBM Docling
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* PDF Upload Area */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
              dragActive 
                ? "border-blue-400 bg-blue-50 dark:bg-blue-950" 
                : "border-slate-300 dark:border-slate-600 hover:border-blue-400"
            }`}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
            onClick={() => document.getElementById('pdf-input')?.click()}
          >
            <input
              id="pdf-input"
              type="file"
              className="hidden"
              accept=".pdf"
              onChange={handleFileInput}
              disabled={isAnalyzing}
            />
            
            {isAnalyzing ? (
              <div className="space-y-4">
                <Loader2 className="h-12 w-12 text-blue-600 mx-auto animate-spin" />
                <p className="text-slate-600 dark:text-slate-300">
                  Analyzing PDF structure...
                </p>
              </div>
            ) : (
              <>
                <FileText className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600 dark:text-slate-300 mb-2">
                  {dragActive ? "Drop your PDF here" : "Drop PDF here or click to browse"}
                </p>
                <p className="text-sm text-slate-500">
                  Supports PDF files up to 50MB
                </p>
              </>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Quick Analysis Results */}
          {analysis && (
            <Card>
              <CardContent className="pt-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{analysis.metadata.page_count}</div>
                    <div className="text-muted-foreground">Pages</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {Math.round(analysis.text_length / 1000)}K
                    </div>
                    <div className="text-muted-foreground">Characters</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {analysis.structure.tables.length}
                    </div>
                    <div className="text-muted-foreground">Tables</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">
                      {analysis.structure.sections.length}
                    </div>
                    <div className="text-muted-foreground">Sections</div>
                  </div>
                </div>
                
                <div className="mt-4 flex gap-2 justify-center">
                  <Button
                    variant="outline"
                    onClick={() => setShowDetails(true)}
                  >
                    <Eye className="mr-2 h-4 w-4" />
                    View Details
                  </Button>
                  
                  {onExtractFromAnalysis && (
                    <Button onClick={handleExtractFromPdf}>
                      <Zap className="mr-2 h-4 w-4" />
                      Extract Knowledge
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Detailed Analysis Dialog */}
      <PdfAnalysisDialog 
        analysis={analysis}
        open={showDetails}
        onOpenChange={setShowDetails}
        onExtract={handleExtractFromPdf}
        showExtractButton={!!onExtractFromAnalysis}
      />
    </div>
  );
}

interface PdfAnalysisDialogProps {
  analysis: PdfAnalysis | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onExtract?: () => void;
  showExtractButton?: boolean;
}

function PdfAnalysisDialog({ 
  analysis, 
  open, 
  onOpenChange, 
  onExtract, 
  showExtractButton 
}: PdfAnalysisDialogProps) {
  if (!analysis) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            PDF Analysis: {analysis.filename}
          </DialogTitle>
          <DialogDescription>
            Detailed document structure and content analysis
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Recommended Extraction Type */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-600" />
                Extraction Recommendation
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <Badge variant="outline" className="capitalize text-lg px-4 py-2">
                  {analysis.recommended_extraction_type}
                </Badge>
                <span className="text-muted-foreground">
                  Recommended based on document content analysis
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Document Metadata */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Document Information</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Title:</span>
                  <span>{analysis.metadata.title || 'N/A'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Author:</span>
                  <span>{analysis.metadata.author || 'N/A'}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Pages:</span>
                  <span>{analysis.metadata.page_count}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Created:</span>
                  <span>{analysis.metadata.creation_date || 'N/A'}</span>
                </div>
                {analysis.metadata.subject && (
                  <div className="flex items-center gap-2 md:col-span-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">Subject:</span>
                    <span>{analysis.metadata.subject}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Document Structure */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Document Structure</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center p-4 border rounded-lg">
                  <Table className="h-8 w-8 mx-auto mb-2 text-blue-600" />
                  <div className="text-2xl font-bold">{analysis.structure.tables.length}</div>
                  <div className="text-sm text-muted-foreground">Tables</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <ImageIcon className="h-8 w-8 mx-auto mb-2 text-green-600" />
                  <div className="text-2xl font-bold">
                    {analysis.structure.has_images ? 'Yes' : 'No'}
                  </div>
                  <div className="text-sm text-muted-foreground">Images</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <BookOpen className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                  <div className="text-2xl font-bold">{analysis.structure.sections.length}</div>
                  <div className="text-sm text-muted-foreground">Sections</div>
                </div>
                <div className="text-center p-4 border rounded-lg">
                  <FileText className="h-8 w-8 mx-auto mb-2 text-orange-600" />
                  <div className="text-2xl font-bold">
                    {Math.round(analysis.size_bytes / 1024)}KB
                  </div>
                  <div className="text-sm text-muted-foreground">File Size</div>
                </div>
              </div>

              {/* Tables Information */}
              {analysis.structure.tables.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">Tables Found:</h4>
                  {analysis.structure.tables.map((table, index) => (
                    <div key={index} className="text-sm p-2 bg-muted rounded">
                      Table {table.table_id + 1}: {table.rows}×{table.cols} on page {table.page + 1}
                    </div>
                  ))}
                </div>
              )}

              {/* Sections Information */}
              {analysis.structure.sections.length > 0 && (
                <div className="space-y-2 mt-4">
                  <h4 className="font-medium">Document Sections:</h4>
                  {analysis.structure.sections.slice(0, 5).map((section, index) => (
                    <div key={index} className="text-sm p-2 bg-muted rounded">
                      <span className="font-medium">Level {section.level}:</span> {section.text}
                    </div>
                  ))}
                  {analysis.structure.sections.length > 5 && (
                    <div className="text-sm text-muted-foreground">
                      ... and {analysis.structure.sections.length - 5} more sections
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Content Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Content Preview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-muted p-4 rounded-lg text-sm font-mono max-h-60 overflow-y-auto">
                {analysis.preview}
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          {showExtractButton && onExtract && (
            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Close
              </Button>
              <Button onClick={onExtract}>
                <Zap className="mr-2 h-4 w-4" />
                Extract Knowledge
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}