# Project Structure

## Directory Organization

```
├── app/                    # Next.js App Router directory
│   ├── favicon.ico        # Site favicon
│   ├── globals.css        # Global CSS styles and Tailwind imports
│   ├── layout.tsx         # Root layout component
│   └── page.tsx           # Home page component
├── lib/                   # Utility functions and shared logic
│   └── utils.ts           # Common utilities (cn function for class merging)
├── public/                # Static assets served from root
│   ├── *.svg              # SVG icons and logos
└── components/            # Reusable React components (shadcn/ui target)
    └── ui/                # shadcn/ui components location
```

## File Naming Conventions
- **Components**: PascalCase for component files (`MyComponent.tsx`)
- **Pages**: lowercase for route files (`page.tsx`, `layout.tsx`)
- **Utilities**: camelCase for utility files (`utils.ts`)
- **Static assets**: lowercase with hyphens (`next.svg`)

## Import Patterns
- Use `@/` alias for imports from project root
- Prefer named imports over default imports where possible
- Group imports: external packages first, then internal modules

## Component Organization
- **App Router**: All routes in `app/` directory
- **Shared components**: Place in `components/` directory
- **UI components**: shadcn/ui components go in `components/ui/`
- **Utilities**: Shared functions in `lib/` directory

## Styling Approach
- **Global styles**: `app/globals.css` for Tailwind and global CSS
- **Component styles**: Inline Tailwind classes
- **Utility classes**: Use `cn()` function from `lib/utils.ts` for conditional classes
- **CSS Variables**: Defined in globals.css for theming

## Path Aliases (tsconfig.json)
- `@/*` → Project root
- `@/components` → Components directory
- `@/lib` → Library/utilities directory
- `@/ui` → UI components directory