# Technology Stack

## Core Framework
- **Next.js 15.5.2** with App Router architecture
- **React 19.1.0** with React DOM 19.1.0
- **TypeScript 5** for type safety

## Build System & Package Management
- **Turbopack** for fast development and builds
- **pnpm** as package manager (lock file present)
- **ESLint 9** for code linting with Next.js config

## Styling & UI
- **Tailwind CSS 4** for utility-first styling
- **shadcn/ui** components (New York style variant)
- **Lucide React** for icons
- **class-variance-authority** and **clsx** for conditional styling
- **tailwind-merge** for class merging utilities

## Development Tools
- **PostCSS** for CSS processing
- Path aliases configured (`@/*` maps to root)
- CSS variables enabled for theming

## Common Commands

### Development
```bash
pnpm dev          # Start development server with Turbopack
pnpm build        # Build for production with Turbopack
pnpm start        # Start production server
pnpm lint         # Run ESLint
```

### shadcn/ui Components
```bash
npx shadcn@latest add [component]  # Add shadcn/ui components
```

## Configuration Notes
- Uses `@/` path alias for imports from project root
- Strict TypeScript configuration enabled
- CSS variables for theming support
- RSC (React Server Components) enabled