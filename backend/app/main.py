"""
Fast Document Processing Application (DPA) using LangExtract
Integrated with Next.js frontend for knowledge extraction
"""

import os
import asyncio
import uuid
import logging
import time
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import aiofiles
import langextract as lx
from dotenv import load_dotenv
from .pdf_processor import process_pdf_bytes

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('knowledgeai.log')
    ]
)

logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="KnowledgeAI DPA",
    description="Fast Document Processing Application using Google's LangExtract",
    version="1.0.0"
)

logger.info("🚀 KnowledgeAI DPA starting up...")
logger.info(f"📊 Environment: DEBUG={os.getenv('DEBUG', 'True')}")
logger.info(f"🔑 API Key configured: {'Yes' if os.getenv('LANGEXTRACT_API_KEY') else 'No'}")
logger.info(f"🌐 CORS Origins: {os.getenv('ALLOWED_ORIGINS', 'http://localhost:3000')}")

# CORS middleware for Next.js integration
allowed_origins = os.getenv("ALLOWED_ORIGINS", "http://localhost:3000").split(",")
app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# Request logging middleware
@app.middleware("http")
async def log_requests(request, call_next):
    start_time = time.time()
    
    # Log request
    logger.info(f"🌐 {request.method} {request.url.path}")
    if request.query_params:
        logger.info(f"   📋 Query params: {dict(request.query_params)}")
    
    # Handle OPTIONS requests for CORS
    if request.method == "OPTIONS":
        response = await call_next(request)
        process_time = time.time() - start_time
        logger.info(f"✅ {request.method} {request.url.path} - {response.status_code} ({process_time:.3f}s)")
        return response
    
    # Process request
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(f"✅ {request.method} {request.url.path} - {response.status_code} ({process_time:.3f}s)")
    
    return response

# In-memory storage for demo (use Redis/DB in production)
extraction_jobs = {}
extraction_results = {}

class ExtractionRequest(BaseModel):
    text: str
    extraction_type: str = "general"
    custom_prompt: Optional[str] = None
    model_id: str = "gemini-2.5-flash"

class ExtractionJob(BaseModel):
    job_id: str
    status: str  # "pending", "processing", "completed", "failed"
    created_at: datetime
    completed_at: Optional[datetime] = None
    progress: int = 0
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

# Predefined extraction templates
EXTRACTION_TEMPLATES = {
    "general": {
        "prompt": """Extract key information including entities, concepts, relationships, and important facts.
        Focus on names, dates, locations, organizations, key concepts, and actionable items.
        Provide meaningful attributes for context.""",
        "examples": [
            lx.data.ExampleData(
                text="John Smith, CEO of TechCorp, announced a $50M funding round on January 15, 2024 in San Francisco.",
                extractions=[
                    lx.data.Extraction(
                        extraction_class="person",
                        extraction_text="John Smith",
                        attributes={"role": "CEO", "organization": "TechCorp"}
                    ),
                    lx.data.Extraction(
                        extraction_class="organization",
                        extraction_text="TechCorp",
                        attributes={"type": "company"}
                    ),
                    lx.data.Extraction(
                        extraction_class="financial",
                        extraction_text="$50M funding round",
                        attributes={"amount": "50000000", "type": "funding"}
                    ),
                    lx.data.Extraction(
                        extraction_class="date",
                        extraction_text="January 15, 2024",
                        attributes={"event": "funding announcement"}
                    ),
                    lx.data.Extraction(
                        extraction_class="location",
                        extraction_text="San Francisco",
                        attributes={"context": "announcement location"}
                    )
                ]
            )
        ]
    },
    "medical": {
        "prompt": """Extract medical information including medications, dosages, conditions, procedures, and patient details.
        Focus on drug names, dosages, administration routes, medical conditions, and treatment plans.""",
        "examples": [
            lx.data.ExampleData(
                text="Patient prescribed Metformin 500mg twice daily for Type 2 diabetes management.",
                extractions=[
                    lx.data.Extraction(
                        extraction_class="medication",
                        extraction_text="Metformin",
                        attributes={"dosage": "500mg", "frequency": "twice daily"}
                    ),
                    lx.data.Extraction(
                        extraction_class="condition",
                        extraction_text="Type 2 diabetes",
                        attributes={"type": "chronic condition"}
                    )
                ]
            )
        ]
    },
    "legal": {
        "prompt": """Extract legal entities, clauses, obligations, dates, and key terms.
        Focus on parties, contract terms, deadlines, obligations, and legal concepts.""",
        "examples": [
            lx.data.ExampleData(
                text="The Agreement shall terminate on December 31, 2024, unless renewed by mutual consent of both parties.",
                extractions=[
                    lx.data.Extraction(
                        extraction_class="date",
                        extraction_text="December 31, 2024",
                        attributes={"type": "termination_date"}
                    ),
                    lx.data.Extraction(
                        extraction_class="clause",
                        extraction_text="terminate on December 31, 2024, unless renewed by mutual consent",
                        attributes={"type": "termination_clause"}
                    )
                ]
            )
        ]
    },
    "financial": {
        "prompt": """Extract financial data including amounts, percentages, financial instruments, and economic indicators.
        Focus on monetary values, financial metrics, investment details, and market data.""",
        "examples": [
            lx.data.ExampleData(
                text="The company reported Q3 revenue of $2.5B, representing a 15% increase year-over-year.",
                extractions=[
                    lx.data.Extraction(
                        extraction_class="revenue",
                        extraction_text="$2.5B",
                        attributes={"period": "Q3", "type": "quarterly_revenue"}
                    ),
                    lx.data.Extraction(
                        extraction_class="growth",
                        extraction_text="15% increase",
                        attributes={"period": "year-over-year", "type": "revenue_growth"}
                    )
                ]
            )
        ]
    }
}

async def process_extraction_task(job_id: str, text: str, extraction_type: str, custom_prompt: Optional[str], model_id: str):
    """Background task to process document extraction"""
    start_time = time.time()
    logger.info(f"🔄 Starting extraction job {job_id}")
    logger.info(f"📝 Text length: {len(text)} characters")
    logger.info(f"🎯 Extraction type: {extraction_type}")
    logger.info(f"🤖 Model: {model_id}")
    
    try:
        # Update job status
        extraction_jobs[job_id].status = "processing"
        extraction_jobs[job_id].progress = 10
        logger.info(f"📊 Job {job_id}: Status updated to processing (10%)")
        
        # Get extraction template
        template = EXTRACTION_TEMPLATES.get(extraction_type, EXTRACTION_TEMPLATES["general"])
        prompt = custom_prompt if custom_prompt else template["prompt"]
        examples = template["examples"]
        
        logger.info(f"📋 Job {job_id}: Using template '{extraction_type}' with {len(examples)} examples")
        logger.info(f"💭 Job {job_id}: Prompt preview: {prompt[:100]}...")
        
        extraction_jobs[job_id].progress = 30
        logger.info(f"📊 Job {job_id}: Template loaded (30%)")
        
        # Run LangExtract
        logger.info(f"🚀 Job {job_id}: Starting LangExtract processing...")
        langextract_start = time.time()
        
        result = lx.extract(
            text_or_documents=text,
            prompt_description=prompt,
            examples=examples,
            model_id=model_id,
            extraction_passes=2,  # Multiple passes for better recall
            max_workers=10,       # Parallel processing for speed
        )
        
        langextract_time = time.time() - langextract_start
        logger.info(f"⚡ Job {job_id}: LangExtract completed in {langextract_time:.2f}s")
        logger.info(f"📊 Job {job_id}: Found {len(result.extractions)} extractions")
        
        extraction_jobs[job_id].progress = 80
        logger.info(f"📊 Job {job_id}: Processing results (80%)")
        
        # Process results
        processed_result = {
            "extractions": [],
            "metadata": {
                "total_extractions": len(result.extractions),
                "text_length": len(text),
                "model_used": model_id,
                "extraction_type": extraction_type,
                "processing_time": langextract_time
            }
        }
        
        # Log extraction details
        extraction_classes = {}
        for extraction in result.extractions:
            class_name = extraction.extraction_class
            extraction_classes[class_name] = extraction_classes.get(class_name, 0) + 1
            
            processed_result["extractions"].append({
                "class": extraction.extraction_class,
                "text": extraction.extraction_text,
                "attributes": extraction.attributes,
                "start_char": extraction.start_char,
                "end_char": extraction.end_char,
                "confidence": getattr(extraction, 'confidence', None)
            })
        
        logger.info(f"📈 Job {job_id}: Extraction breakdown: {extraction_classes}")
        
        # Complete job
        total_time = time.time() - start_time
        extraction_jobs[job_id].status = "completed"
        extraction_jobs[job_id].progress = 100
        extraction_jobs[job_id].completed_at = datetime.now()
        extraction_jobs[job_id].result = processed_result
        
        logger.info(f"✅ Job {job_id}: Completed successfully in {total_time:.2f}s")
        logger.info(f"📊 Job {job_id}: Final stats - {len(result.extractions)} extractions from {len(text)} chars")
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"❌ Job {job_id}: Failed after {total_time:.2f}s - {str(e)}")
        logger.error(f"🔍 Job {job_id}: Error details", exc_info=True)
        
        extraction_jobs[job_id].status = "failed"
        extraction_jobs[job_id].error = str(e)
        extraction_jobs[job_id].completed_at = datetime.now()

@app.get("/")
async def root():
    """Health check endpoint"""
    logger.info("💓 Health check requested")
    return {"message": "KnowledgeAI DPA is running", "status": "healthy"}

@app.options("/api/extraction-types")
async def extraction_types_options():
    """Handle CORS preflight for extraction-types endpoint"""
    return {"message": "OK"}

@app.get("/api/extraction-types")
async def get_extraction_types():
    """Get available extraction types"""
    logger.info("📋 Extraction types requested")
    types = list(EXTRACTION_TEMPLATES.keys())
    logger.info(f"📋 Available types: {types}")
    return {
        "types": types,
        "templates": {
            name: {"prompt": template["prompt"]}
            for name, template in EXTRACTION_TEMPLATES.items()
        }
    }

@app.options("/api/extract")
async def extract_options():
    """Handle CORS preflight for extract endpoint"""
    return {"message": "OK"}

@app.options("/api/extract")
async def extract_options():
    """Handle CORS preflight for extract endpoint"""
    return {"message": "OK"}

@app.post("/api/extract")
async def extract_knowledge(
    request: ExtractionRequest,
    background_tasks: BackgroundTasks
):
    """Start knowledge extraction from text"""
    logger.info("🎯 New extraction request received")
    logger.info(f"📝 Text length: {len(request.text)} characters")
    logger.info(f"🎯 Extraction type: {request.extraction_type}")
    logger.info(f"🤖 Model: {request.model_id}")
    logger.info(f"💭 Custom prompt: {'Yes' if request.custom_prompt else 'No'}")
    
    # Validate API key
    api_key = os.getenv("LANGEXTRACT_API_KEY")
    if not api_key:
        logger.error("❌ LangExtract API key not configured")
        raise HTTPException(
            status_code=500,
            detail="LangExtract API key not configured. Please set LANGEXTRACT_API_KEY environment variable."
        )
    
    # Check for placeholder/invalid keys
    invalid_keys = [
        "your-api-key-here", 
        "YOUR_ACTUAL_GEMINI_API_KEY_HERE",
        "PUT_YOUR_REAL_GEMINI_API_KEY_HERE",
        "PASTE_YOUR_REAL_API_KEY_HERE"
    ]
    
    if api_key in invalid_keys:
        logger.error("❌ Using placeholder API key! Please set a valid LANGEXTRACT_API_KEY")
        logger.error("🔗 Get your API key at: https://aistudio.google.com/app/apikey")
        raise HTTPException(
            status_code=500, 
            detail="Invalid API key - please configure a valid Gemini API key from https://aistudio.google.com/app/apikey"
        )
    
    if not api_key.startswith("AIza"):
        logger.warning("⚠️  API key doesn't start with 'AIza' - this might not be a valid Gemini API key")
    
    logger.info(f"🔑 Using API key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else '****'}")
    
    # Create job
    job_id = str(uuid.uuid4())
    job = ExtractionJob(
        job_id=job_id,
        status="pending",
        created_at=datetime.now()
    )
    
    extraction_jobs[job_id] = job
    logger.info(f"📋 Created job {job_id}")
    
    # Start background processing
    background_tasks.add_task(
        process_extraction_task,
        job_id,
        request.text,
        request.extraction_type,
        request.custom_prompt,
        request.model_id
    )
    
    logger.info(f"🚀 Background task started for job {job_id}")
    return {"job_id": job_id, "status": "pending"}

@app.options("/api/extract-file")
async def extract_file_options():
    """Handle CORS preflight for extract-file endpoint"""
    return {"message": "OK"}

@app.post("/api/extract-file")
async def extract_from_file(
    file: UploadFile = File(...),
    extraction_type: str = "general",
    model_id: str = "gemini-2.5-flash",
    background_tasks: BackgroundTasks = None
):
    """Extract knowledge from uploaded file"""
    logger.info(f"📁 File upload received: {file.filename}")
    logger.info(f"📄 File type: {file.content_type}")
    logger.info(f"🎯 Extraction type: {extraction_type}")
    
    # Validate file type
    allowed_types = ["text/plain", "application/pdf", "text/markdown"]
    if file.content_type not in allowed_types:
        logger.error(f"❌ Unsupported file type: {file.content_type}")
        raise HTTPException(
            status_code=400,
            detail=f"Unsupported file type: {file.content_type}. Supported types: {allowed_types}"
        )
    
    try:
        # Read file content
        logger.info(f"📖 Reading file content...")
        content = await file.read()
        file_size = len(content)
        logger.info(f"📊 File size: {file_size} bytes ({file_size/1024:.1f} KB)")
        
        if file.content_type == "text/plain" or file.content_type == "text/markdown":
            logger.info("📝 Processing as text file")
            text = content.decode("utf-8")
            logger.info(f"📝 Decoded text length: {len(text)} characters")
            
        elif file.content_type == "application/pdf":
            logger.info("📄 Processing as PDF file using Docling")
            pdf_start = time.time()
            
            # Process PDF using Docling
            pdf_data = process_pdf_bytes(content, file.filename or "document.pdf")
            text = pdf_data["text"]
            
            pdf_time = time.time() - pdf_start
            logger.info(f"📄 PDF processed in {pdf_time:.2f}s")
            logger.info(f"📊 PDF stats: {pdf_data['metadata']['page_count']} pages, {len(text)} chars")
            logger.info(f"📋 PDF structure: {len(pdf_data['structure']['tables'])} tables, {len(pdf_data['structure']['sections'])} sections")
            
            # Add PDF metadata to the extraction context
            if pdf_data.get("metadata"):
                text = f"""Document Metadata:
Title: {pdf_data['metadata'].get('title', 'N/A')}
Author: {pdf_data['metadata'].get('author', 'N/A')}
Pages: {pdf_data['metadata'].get('page_count', 'N/A')}
Filename: {pdf_data['metadata'].get('filename', 'N/A')}

Document Content:
{text}"""
                logger.info("📋 Added PDF metadata to extraction context")
        else:
            logger.error(f"❌ Unsupported file type: {file.content_type}")
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type: {file.content_type}"
            )
        
        # Create extraction request
        logger.info("🎯 Creating extraction request from file content")
        request = ExtractionRequest(
            text=text,
            extraction_type=extraction_type,
            model_id=model_id
        )
        
        return await extract_knowledge(request, background_tasks)
        
    except Exception as e:
        logger.error(f"❌ File processing error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

@app.options("/api/job/{job_id}")
async def job_status_options(job_id: str):
    """Handle CORS preflight for job status endpoint"""
    return {"message": "OK"}

@app.get("/api/job/{job_id}")
async def get_job_status(job_id: str):
    """Get extraction job status and results"""
    logger.debug(f"📊 Status check for job {job_id}")
    
    if job_id not in extraction_jobs:
        logger.warning(f"❓ Job not found: {job_id}")
        raise HTTPException(status_code=404, detail="Job not found")
    
    job = extraction_jobs[job_id]
    logger.debug(f"📊 Job {job_id}: status={job.status}, progress={job.progress}%")
    
    return {
        "job_id": job_id,
        "status": job.status,
        "progress": job.progress,
        "created_at": job.created_at,
        "completed_at": job.completed_at,
        "result": job.result,
        "error": job.error
    }

@app.get("/api/jobs")
async def list_jobs():
    """List all extraction jobs"""
    return {
        "jobs": [
            {
                "job_id": job_id,
                "status": job.status,
                "created_at": job.created_at,
                "completed_at": job.completed_at
            }
            for job_id, job in extraction_jobs.items()
        ]
    }

@app.delete("/api/job/{job_id}")
async def delete_job(job_id: str):
    """Delete extraction job and results"""
    
    if job_id not in extraction_jobs:
        raise HTTPException(status_code=404, detail="Job not found")
    
    del extraction_jobs[job_id]
    
    return {"message": "Job deleted successfully"}

@app.post("/api/analyze-pdf")
async def analyze_pdf_structure(
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = None
):
    """Analyze PDF structure and provide detailed document information"""
    logger.info(f"🔍 PDF analysis requested: {file.filename}")
    
    if file.content_type != "application/pdf":
        logger.error(f"❌ Invalid file type for PDF analysis: {file.content_type}")
        raise HTTPException(
            status_code=400,
            detail="Only PDF files are supported for this endpoint"
        )
    
    try:
        # Read PDF content
        logger.info("📖 Reading PDF content for analysis...")
        content = await file.read()
        file_size = len(content)
        logger.info(f"📊 PDF file size: {file_size} bytes ({file_size/1024:.1f} KB)")
        
        # Process PDF with Docling
        logger.info("🔍 Starting PDF analysis with Docling...")
        analysis_start = time.time()
        
        pdf_data = process_pdf_bytes(content, file.filename or "document.pdf")
        
        analysis_time = time.time() - analysis_start
        logger.info(f"🔍 PDF analysis completed in {analysis_time:.2f}s")
        
        # Log analysis results
        logger.info(f"📊 Analysis results:")
        logger.info(f"   📄 Pages: {pdf_data['metadata']['page_count']}")
        logger.info(f"   📝 Text length: {len(pdf_data['text'])} characters")
        logger.info(f"   📋 Tables: {len(pdf_data['structure']['tables'])}")
        logger.info(f"   📑 Sections: {len(pdf_data['structure']['sections'])}")
        logger.info(f"   🖼️  Images: {'Yes' if pdf_data['structure']['has_images'] else 'No'}")
        
        # Get recommendation
        recommended_type = _recommend_extraction_type(pdf_data["text"])
        logger.info(f"🎯 Recommended extraction type: {recommended_type}")
        
        # Return detailed analysis
        return {
            "filename": file.filename,
            "size_bytes": len(content),
            "text_length": len(pdf_data["text"]),
            "metadata": pdf_data["metadata"],
            "structure": pdf_data["structure"],
            "preview": pdf_data["text"][:1000] + "..." if len(pdf_data["text"]) > 1000 else pdf_data["text"],
            "extraction_ready": True,
            "recommended_extraction_type": recommended_type
        }
        
    except Exception as e:
        logger.error(f"❌ PDF analysis failed: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"PDF analysis failed: {str(e)}")

def _recommend_extraction_type(text: str) -> str:
    """Recommend extraction type based on document content"""
    logger.debug("🎯 Analyzing text for extraction type recommendation...")
    text_lower = text.lower()
    
    # Medical keywords
    medical_keywords = ["patient", "diagnosis", "medication", "treatment", "doctor", "hospital", "medical", "clinical"]
    medical_score = sum(1 for keyword in medical_keywords if keyword in text_lower)
    
    # Legal keywords
    legal_keywords = ["contract", "agreement", "clause", "party", "legal", "court", "law", "attorney"]
    legal_score = sum(1 for keyword in legal_keywords if keyword in text_lower)
    
    # Financial keywords
    financial_keywords = ["revenue", "profit", "financial", "investment", "earnings", "budget", "cost", "income"]
    financial_score = sum(1 for keyword in financial_keywords if keyword in text_lower)
    
    # Determine recommendation
    scores = {
        "medical": medical_score,
        "legal": legal_score,
        "financial": financial_score
    }
    
    logger.debug(f"🎯 Keyword scores: medical={medical_score}, legal={legal_score}, financial={financial_score}")
    
    max_score = max(scores.values())
    if max_score >= 3:  # Threshold for specialized extraction
        recommendation = max(scores, key=scores.get)
        logger.debug(f"🎯 Specialized recommendation: {recommendation} (score: {max_score})")
        return recommendation
    
    logger.debug("🎯 General recommendation (no specialized pattern detected)")
    return "general"

if __name__ == "__main__":
    import uvicorn
    
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", 8000))
    debug = os.getenv("DEBUG", "True").lower() == "true"
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=debug,
        log_level="info"
    )