"""Real-time log management for WebSocket streaming"""

import asyncio
import json
import logging
import time
from collections import deque
from datetime import datetime
from typing import Dict, List, Set
from fastapi import WebSocket

class WebSocketLogHandler(logging.Handler):
    """Custom log handler that broadcasts logs to WebSocket clients"""
    
    def __init__(self):
        super().__init__()
        self.clients: Set[WebSocket] = set()
        self.log_buffer = deque(maxlen=1000)  # Keep last 1000 logs
        
    def add_client(self, websocket: WebSocket):
        """Add a WebSocket client"""
        self.clients.add(websocket)
        
    def remove_client(self, websocket: WebSocket):
        """Remove a WebSocket client"""
        self.clients.discard(websocket)
        
    def emit(self, record: logging.LogRecord):
        """Emit log record to all connected clients"""
        try:
            # Format the log record
            log_entry = {
                "timestamp": datetime.fromtimestamp(record.created).isoformat(),
                "level": record.levelname,
                "logger": record.name,
                "message": record.getMessage(),
                "module": record.module,
                "function": record.funcName,
                "line": record.lineno
            }
            
            # Add to buffer
            self.log_buffer.append(log_entry)
            
            # Broadcast to all clients
            if self.clients:
                asyncio.create_task(self._broadcast_log(log_entry))
                
        except Exception as e:
            # Don't let logging errors break the application
            print(f"Error in WebSocket log handler: {e}")
    
    async def _broadcast_log(self, log_entry: Dict):
        """Broadcast log entry to all connected clients"""
        if not self.clients:
            return
            
        # Create a copy of clients to avoid modification during iteration
        clients_copy = self.clients.copy()
        
        for client in clients_copy:
            try:
                await client.send_text(json.dumps(log_entry))
            except Exception:
                # Remove disconnected clients
                self.clients.discard(client)
    
    def get_recent_logs(self, limit: int = 100) -> List[Dict]:
        """Get recent logs from buffer"""
        return list(self.log_buffer)[-limit:]

class LogManager:
    """Manages log streaming and statistics"""
    
    def __init__(self):
        self.websocket_handler = WebSocketLogHandler()
        self.stats = {
            "total_logs": 0,
            "error_count": 0,
            "warning_count": 0,
            "info_count": 0,
            "debug_count": 0,
            "start_time": time.time()
        }
        
        # Add our handler to the root logger
        root_logger = logging.getLogger()
        root_logger.addHandler(self.websocket_handler)
        
        # Also add to specific loggers
        app_logger = logging.getLogger("app.main")
        app_logger.addHandler(self.websocket_handler)
        
        pdf_logger = logging.getLogger("app.pdf_processor")
        pdf_logger.addHandler(self.websocket_handler)
    
    def add_websocket_client(self, websocket: WebSocket):
        """Add WebSocket client for log streaming"""
        self.websocket_handler.add_client(websocket)
    
    def remove_websocket_client(self, websocket: WebSocket):
        """Remove WebSocket client"""
        self.websocket_handler.remove_client(websocket)
    
    def get_recent_logs(self, limit: int = 100) -> List[Dict]:
        """Get recent logs"""
        return self.websocket_handler.get_recent_logs(limit)
    
    def get_stats(self) -> Dict:
        """Get logging statistics"""
        uptime = time.time() - self.stats["start_time"]
        
        # Count logs by level from buffer
        logs = self.websocket_handler.log_buffer
        level_counts = {"ERROR": 0, "WARNING": 0, "INFO": 0, "DEBUG": 0}
        
        for log in logs:
            level = log.get("level", "INFO")
            if level in level_counts:
                level_counts[level] += 1
        
        return {
            "total_logs": len(logs),
            "error_count": level_counts["ERROR"],
            "warning_count": level_counts["WARNING"],
            "info_count": level_counts["INFO"],
            "debug_count": level_counts["DEBUG"],
            "uptime_seconds": uptime,
            "connected_clients": len(self.websocket_handler.clients)
        }

# Global log manager instance
log_manager = LogManager()