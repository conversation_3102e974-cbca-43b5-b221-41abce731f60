"""
PDF Processing Module using IBM's Docling
Handles PDF document parsing and text extraction with structure preservation
"""

import logging
import time
from typing import Dict, Any, Optional
from pathlib import Path

from docling.document_converter import DocumentConverter
from docling.datamodel.base_models import InputFormat
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.backend.pypdfium2_backend import PyPdfiumDocumentBackend

# Configure logging for PDF processor
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PDFProcessor:
    """Enhanced PDF processor using IBM's Docling for structured document parsing"""
    
    def __init__(self):
        """Initialize the PDF processor with optimized settings"""
        logger.info("🔧 Initializing PDF processor with Docling...")
        
        # Configure pipeline options for better performance and accuracy
        pipeline_options = PdfPipelineOptions()
        pipeline_options.do_ocr = True  # Enable OCR for scanned PDFs
        pipeline_options.do_table_structure = True  # Extract table structures
        pipeline_options.table_structure_options.do_cell_matching = True
        
        logger.info("📋 PDF pipeline options:")
        logger.info(f"   🔍 OCR enabled: {pipeline_options.do_ocr}")
        logger.info(f"   📊 Table structure: {pipeline_options.do_table_structure}")
        logger.info(f"   🔗 Cell matching: {pipeline_options.table_structure_options.do_cell_matching}")
        
        # Initialize document converter with PDF backend
        self.converter = DocumentConverter(
            format_options={
                InputFormat.PDF: pipeline_options,
            }
        )
        
        logger.info("✅ PDF processor initialized successfully")
    
    def extract_text_from_bytes(self, pdf_bytes: bytes, filename: str = "document.pdf") -> Dict[str, Any]:
        """
        Extract structured text from PDF bytes
        
        Args:
            pdf_bytes: PDF file content as bytes
            filename: Original filename for context
            
        Returns:
            Dictionary containing extracted text and metadata
        """
        start_time = time.time()
        logger.info(f"📄 Starting PDF processing: {filename}")
        logger.info(f"📊 PDF size: {len(pdf_bytes)} bytes ({len(pdf_bytes)/1024:.1f} KB)")
        
        try:
            # Create a temporary file from bytes for Docling
            logger.info("🔄 Creating temporary PDF file...")
            import tempfile
            import os

            # Create a temporary file with the PDF content
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(pdf_bytes)
                temp_file_path = temp_file.name

            try:
                # Convert PDF to structured document using file path
                logger.info("🚀 Starting Docling conversion...")
                conversion_start = time.time()

                result = self.converter.convert(temp_file_path)

                conversion_time = time.time() - conversion_start
                logger.info(f"⚡ Docling conversion completed in {conversion_time:.2f}s")
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                    logger.debug("🗑️ Temporary file cleaned up")
                except Exception as cleanup_error:
                    logger.warning(f"⚠️ Could not clean up temporary file: {cleanup_error}")
            
            # Extract the document
            doc = result.document
            logger.info("📋 Extracting document structure...")
            
            # Get full text content
            export_start = time.time()
            full_text = doc.export_to_markdown()
            export_time = time.time() - export_start
            
            logger.info(f"📝 Text export completed in {export_time:.2f}s")
            logger.info(f"📊 Extracted text length: {len(full_text)} characters")
            
            # Extract structured information
            logger.info("🔍 Analyzing document structure...")
            structure_start = time.time()
            
            page_count = len(doc.pages) if hasattr(doc, 'pages') else 0
            has_tables = self._has_tables(doc)
            has_images = self._has_images(doc)
            sections = self._extract_sections(doc)
            tables = self._extract_tables(doc)
            
            structure_time = time.time() - structure_start
            logger.info(f"🔍 Structure analysis completed in {structure_time:.2f}s")
            
            # Log structure details
            logger.info(f"📊 Document structure:")
            logger.info(f"   📄 Pages: {page_count}")
            logger.info(f"   📋 Tables: {len(tables)} found")
            logger.info(f"   📑 Sections: {len(sections)} found")
            logger.info(f"   🖼️  Images: {'Yes' if has_images else 'No'}")
            
            extracted_data = {
                "text": full_text,
                "metadata": {
                    "filename": filename,
                    "page_count": page_count,
                    "title": getattr(doc, 'title', '') or '',
                    "author": getattr(doc, 'author', '') or '',
                    "creation_date": getattr(doc, 'creation_date', '') or '',
                    "modification_date": getattr(doc, 'modification_date', '') or '',
                    "subject": getattr(doc, 'subject', '') or '',
                    "keywords": getattr(doc, 'keywords', '') or '',
                },
                "structure": {
                    "has_tables": has_tables,
                    "has_images": has_images,
                    "sections": sections,
                    "tables": tables,
                }
            }
            
            total_time = time.time() - start_time
            logger.info(f"✅ PDF processing completed successfully in {total_time:.2f}s")
            logger.info(f"📊 Final stats: {filename} - {page_count} pages, {len(full_text)} chars, {len(tables)} tables")
            
            return extracted_data
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"❌ PDF processing failed after {total_time:.2f}s: {filename}")
            logger.error(f"🔍 Error details: {str(e)}", exc_info=True)
            raise Exception(f"PDF processing failed: {str(e)}")
    
    def extract_text_from_file(self, file_path: str) -> Dict[str, Any]:
        """
        Extract structured text from PDF file path
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Dictionary containing extracted text and metadata
        """
        try:
            path = Path(file_path)
            if not path.exists():
                raise FileNotFoundError(f"PDF file not found: {file_path}")
            
            # Read file and process
            with open(file_path, 'rb') as f:
                pdf_bytes = f.read()
            
            return self.extract_text_from_bytes(pdf_bytes, path.name)
            
        except Exception as e:
            logger.error(f"Error reading PDF file {file_path}: {str(e)}")
            raise Exception(f"PDF file processing failed: {str(e)}")
    
    def _has_tables(self, doc) -> bool:
        """Check if document contains tables"""
        try:
            if hasattr(doc, 'tables') and doc.tables:
                return len(doc.tables) > 0
            return False
        except:
            return False
    
    def _has_images(self, doc) -> bool:
        """Check if document contains images"""
        try:
            if hasattr(doc, 'pictures') and doc.pictures:
                return len(doc.pictures) > 0
            return False
        except:
            return False
    
    def _extract_sections(self, doc) -> list:
        """Extract document sections/headings"""
        sections = []
        try:
            # Try to extract headings from the document structure
            if hasattr(doc, 'texts'):
                for text_item in doc.texts:
                    if hasattr(text_item, 'label') and 'heading' in str(text_item.label).lower():
                        sections.append({
                            "text": text_item.text,
                            "level": getattr(text_item, 'level', 1),
                            "page": getattr(text_item, 'page', 0)
                        })
        except Exception as e:
            logger.warning(f"Could not extract sections: {str(e)}")
        
        return sections
    
    def _extract_tables(self, doc) -> list:
        """Extract table information"""
        tables = []
        try:
            if hasattr(doc, 'tables') and doc.tables:
                for i, table in enumerate(doc.tables):
                    table_data = {
                        "table_id": i,
                        "page": getattr(table, 'page', 0),
                        "rows": getattr(table, 'num_rows', 0),
                        "cols": getattr(table, 'num_cols', 0),
                        "text": str(table) if table else ""
                    }
                    tables.append(table_data)
        except Exception as e:
            logger.warning(f"Could not extract tables: {str(e)}")
        
        return tables

# Global PDF processor instance
pdf_processor = PDFProcessor()

def process_pdf_bytes(pdf_bytes: bytes, filename: str = "document.pdf") -> Dict[str, Any]:
    """
    Convenience function to process PDF bytes
    
    Args:
        pdf_bytes: PDF content as bytes
        filename: Original filename
        
    Returns:
        Processed PDF data with text and metadata
    """
    return pdf_processor.extract_text_from_bytes(pdf_bytes, filename)

def process_pdf_file(file_path: str) -> Dict[str, Any]:
    """
    Convenience function to process PDF file
    
    Args:
        file_path: Path to PDF file
        
    Returns:
        Processed PDF data with text and metadata
    """
    return pdf_processor.extract_text_from_file(file_path)