#!/bin/bash

# KnowledgeAI DPA Startup Script

echo "🚀 Starting KnowledgeAI Document Processing Application"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating from .env.example..."
    cp .env.example .env
    echo "📝 Please edit .env file with your API keys before continuing."
    echo "   You need to set LANGEXTRACT_API_KEY with your Gemini API key."
    echo "   Get your API key from: https://aistudio.google.com/app/apikey"
    exit 1
fi

# Check if LANGEXTRACT_API_KEY is set
source .env
if [ -z "$LANGEXTRACT_API_KEY" ]; then
    echo "❌ LANGEXTRACT_API_KEY not set in .env file"
    echo "   Get your API key from: https://aistudio.google.com/app/apikey"
    exit 1
fi

echo "✅ Environment configured"

# Install dependencies if needed
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
fi

echo "📦 Activating virtual environment..."
source venv/bin/activate

echo "📦 Installing dependencies..."
pip install -r requirements.txt

echo "🔧 Setting up PDF processing..."
# Download Docling models if needed
python -c "
try:
    from docling.document_converter import DocumentConverter
    print('✅ Docling models ready')
except Exception as e:
    print(f'⚠️  Docling setup issue: {e}')
    print('   This is normal on first run - models will download automatically')
"

echo "🔥 Starting FastAPI server..."
echo "   API will be available at: http://localhost:8000"
echo "   API docs will be available at: http://localhost:8000/docs"
echo ""
echo "   Make sure your Next.js frontend is running on http://localhost:3000"
echo ""

uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload