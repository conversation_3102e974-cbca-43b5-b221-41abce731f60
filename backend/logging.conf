[loggers]
keys=root,knowledgeai

[handlers]
keys=console<PERSON><PERSON><PERSON>,fileHandler

[formatters]
keys=detailedFormatter

[logger_root]
level=INFO
handlers=consoleHandler,fileHandler

[logger_knowledgeai]
level=INFO
handlers=consoleHandler,fileHandler
qualname=knowledgeai
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=INFO
formatter=detailedFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=FileHandler
level=INFO
formatter=detailedFormatter
args=('knowledgeai.log', 'a')

[formatter_detailedFormatter]
format=%(asctime)s - %(name)s - %(levelname)s - %(message)s
datefmt=%Y-%m-%d %H:%M:%S