version: '3.8'

services:
  api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - LANGEXTRACT_API_KEY=${LANGEXTRACT_API_KEY}
      - REDIS_URL=redis://redis:6379
      - ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
    depends_on:
      - redis
    volumes:
      - ./app:/app/app
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  worker:
    build: .
    command: celery -A app.main worker --loglevel=info
    environment:
      - LANGEXTRACT_API_KEY=${LANGEXTRACT_API_KEY}
      - REDIS_URL=redis://redis:6379
    depends_on:
      - redis
    volumes:
      - ./app:/app/app
    restart: unless-stopped