#!/bin/bash

# Quick script to update API key
# Usage: ./update_api_key.sh YOUR_ACTUAL_API_KEY

if [ -z "$1" ]; then
    echo "❌ Please provide your API key as an argument"
    echo "Usage: ./update_api_key.sh YOUR_ACTUAL_API_KEY"
    echo ""
    echo "Get your API key from: https://aistudio.google.com/app/apikey"
    exit 1
fi

API_KEY="$1"

# Validate key format
if [[ ! "$API_KEY" =~ ^AIza[A-Za-z0-9_-]{35}$ ]]; then
    echo "⚠️  Warning: API key doesn't match expected format (should start with '<PERSON><PERSON>' and be ~39 characters)"
    echo "Are you sure this is correct? Continuing anyway..."
fi

# Update the .env file
sed -i '' "s/LANGEXTRACT_API_KEY=.*/LANGEXTRACT_API_KEY=$API_KEY/" .env

echo "✅ API key updated in .env file"
echo "🔑 Key: ${API_KEY:0:10}...${API_KEY: -4}"

# Test the key
echo ""
echo "🧪 Testing API key..."
source venv/bin/activate && python test_api_key.py

echo ""
echo "🚀 If the test passed, restart your server:"
echo "   ./start.sh"