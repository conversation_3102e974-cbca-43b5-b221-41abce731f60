#!/usr/bin/env python3
"""
Simple test to check if CORS and API are working
"""

import requests
import json

def test_cors_and_api():
    base_url = "http://localhost:8000"
    
    print("🧪 Testing CORS and API...")
    print("=" * 50)
    
    # Test 1: Check if server is running
    try:
        response = requests.get(f"{base_url}/")
        print(f"✅ Server is running: {response.status_code}")
    except Exception as e:
        print(f"❌ Server not running: {e}")
        return False
    
    # Test 2: Test CORS preflight (OPTIONS)
    try:
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'POST',
            'Access-Control-Request-Headers': 'Content-Type'
        }
        response = requests.options(f"{base_url}/api/extract", headers=headers)
        print(f"✅ CORS preflight: {response.status_code}")
        print(f"   CORS headers: {dict(response.headers)}")
    except Exception as e:
        print(f"❌ CORS preflight failed: {e}")
    
    # Test 3: Test actual API call
    try:
        headers = {
            'Content-Type': 'application/json',
            'Origin': 'http://localhost:3000'
        }
        data = {
            "text": "Hello world test",
            "extraction_type": "general"
        }
        response = requests.post(f"{base_url}/api/extract", 
                               json=data, 
                               headers=headers)
        print(f"✅ API call: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"   Job ID: {result.get('job_id', 'N/A')}")
        else:
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"❌ API call failed: {e}")

if __name__ == "__main__":
    test_cors_and_api()