#!/usr/bin/env python3
"""
Simple script to test if your Gemini API key is working
"""

import os
from dotenv import load_dotenv
import requests

# Load environment variables
load_dotenv()

def test_gemini_api_key():
    api_key = os.getenv("LANGEXTRACT_API_KEY")
    
    if not api_key:
        print("❌ No API key found in environment")
        return False
    
    print(f"🔑 Testing API key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else '****'}")
    
    # Test with a simple Gemini API call
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"
    
    payload = {
        "contents": [{
            "parts": [{
                "text": "Hello, just testing if this API key works. Please respond with 'API key is working!'"
            }]
        }]
    }
    
    try:
        response = requests.post(url, json=payload, timeout=10)
        
        if response.status_code == 200:
            print("✅ API key is working!")
            result = response.json()
            if 'candidates' in result and len(result['candidates']) > 0:
                text = result['candidates'][0]['content']['parts'][0]['text']
                print(f"📝 Response: {text}")
            return True
        else:
            print(f"❌ API key test failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API key: {str(e)}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Gemini API Key...")
    print("=" * 50)
    
    success = test_gemini_api_key()
    
    if success:
        print("\n🎉 Your API key is working! You can now use KnowledgeAI.")
    else:
        print("\n🔧 Please check your API key:")
        print("   1. Go to: https://aistudio.google.com/app/apikey")
        print("   2. Create a new API key")
        print("   3. Copy the ENTIRE key (starts with 'AIza')")
        print("   4. Replace PASTE_YOUR_REAL_API_KEY_HERE in backend/.env")
        print("   5. Run this test again: python test_api_key.py")